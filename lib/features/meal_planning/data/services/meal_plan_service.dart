import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../core/services/firebase_functions_service.dart';
import '../models/meal_plan_request.dart';

part 'meal_plan_service.g.dart';

class MealPlanService {
  final FirebaseFunctionsService _functionsService;

  MealPlanService(this._functionsService);

  /// Generate a meal plan using Firebase Functions
  /// Preferences are automatically retrieved from user profile in Firestore
  Future<MealPlanResponse> generateMealPlan({
    int duration = 3,
  }) async {
    try {
      final response = await _functionsService.generateMealPlan(
        duration: duration,
      );

      return MealPlanResponse.fromJson(response);
    } catch (e) {
      throw Exception('Failed to generate meal plan: $e');
    }
  }

  /// Get user's meal plans
  Future<List<MealPlanResponse>> getUserMealPlans({
    required String userId,
    int limit = 10,
    String status = 'active',
  }) async {
    try {
      final response = await _functionsService.getMealPlans(
        limit: limit,
        status: status,
      );

      final mealPlansData = response['mealPlans'] as List<dynamic>? ?? [];

      return mealPlansData
          .map((planData) => MealPlanResponse.fromJson(planData as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch meal plans: $e');
    }
  }

  /// Analyze nutrition for food items
  Future<NutritionInfo> analyzeNutrition({
    required List<String> foodItems,
    String portion = '1 serving',
  }) async {
    try {
      final response = await _functionsService.analyzeNutrition(
        foodItems: foodItems,
        portion: portion,
      );

      final nutritionData = response['nutrition'] as Map<String, dynamic>;
      return NutritionInfo.fromJson(nutritionData);
    } catch (e) {
      throw Exception('Failed to analyze nutrition: $e');
    }
  }

}

@riverpod
MealPlanService mealPlanService(MealPlanServiceRef ref) {
  final functionsService = ref.watch(firebaseFunctionsServiceProvider);
  return MealPlanService(functionsService);
}
