import 'package:freezed_annotation/freezed_annotation.dart';

part 'meal_plan_request.freezed.dart';
part 'meal_plan_request.g.dart';

@freezed
class MealPlanRequest with _$MealPlanRequest {
  const factory MealPlanRequest({
    required String userId,
    required MealPlanPreferences preferences,
    @Default(3) int duration, // days
  }) = _MealPlanRequest;

  factory MealPlanRequest.fromJson(Map<String, dynamic> json) =>
      _$MealPlanRequestFromJson(json);
}

@freezed
class MealPlanPreferences with _$MealPlanPreferences {
  const factory MealPlanPreferences({
    @Default([]) List<String> dietaryRestrictions,
    @Default([]) List<String> allergies,
    @Default([]) List<String> cuisinePreferences,
    @Default(3) int mealsPerDay,
    @Default(2000) int calorieGoal,
    @Default(0) int proteinGoal,
    @Default(0) int carbsGoal,
    @Default(0) int fatGoal,
    @Default(DietType.normal) DietType dietType,
  }) = _MealPlanPreferences;

  factory MealPlanPreferences.fromJson(Map<String, dynamic> json) =>
      _$MealPlanPreferencesFromJson(json);
}

@freezed
class MealPlanResponse with _$MealPlanResponse {
  const factory MealPlanResponse({
    required bool success,
    required GeneratedMealPlan mealPlan,
    required String message,
  }) = _MealPlanResponse;

  factory MealPlanResponse.fromJson(Map<String, dynamic> json) =>
      _$MealPlanResponseFromJson(json);
}

@freezed
class GeneratedMealPlan with _$GeneratedMealPlan {
  const factory GeneratedMealPlan({
    required List<DayMealPlan> days,
    String? rawResponse,
  }) = _GeneratedMealPlan;

  factory GeneratedMealPlan.fromJson(Map<String, dynamic> json) =>
      _$GeneratedMealPlanFromJson(json);
}

@freezed
class DayMealPlan with _$DayMealPlan {
  const factory DayMealPlan({
    required int day,
    required List<GeneratedMeal> meals,
    required NutritionInfo totalNutrition,
  }) = _DayMealPlan;

  factory DayMealPlan.fromJson(Map<String, dynamic> json) =>
      _$DayMealPlanFromJson(json);
}

@freezed
class GeneratedMeal with _$GeneratedMeal {
  const factory GeneratedMeal({
    required String name,
    String? description,
    required String type, // breakfast, lunch, dinner, snack
    required List<MealIngredient> ingredients,
    required List<String> instructions, // General meal instructions
    required NutritionInfo nutrition,
    @Default(30) int preparationTime,
    @Default('easy') String difficulty,
  }) = _GeneratedMeal;

  factory GeneratedMeal.fromJson(Map<String, dynamic> json) =>
      _$GeneratedMealFromJson(json);
}

@freezed
class MealIngredient with _$MealIngredient {
  const factory MealIngredient({
    required String name,
    @Default([]) List<String> instructions, // Specific instructions for this ingredient
    @Default(false) bool needsPreparation, // Whether this ingredient needs preparation
  }) = _MealIngredient;

  factory MealIngredient.fromJson(Map<String, dynamic> json) =>
      _$MealIngredientFromJson(json);
}

@freezed
class NutritionInfo with _$NutritionInfo {
  const factory NutritionInfo({
    required int calories,
    required double protein,
    required double carbs,
    required double fat,
    @Default(0.0) double fiber,
  }) = _NutritionInfo;

  factory NutritionInfo.fromJson(Map<String, dynamic> json) =>
      _$NutritionInfoFromJson(json);
}

enum DietType {
  @JsonValue('normal')
  normal,
  @JsonValue('keto')
  keto,
  @JsonValue('mediterranean')
  mediterranean,
  @JsonValue('vegetarian')
  vegetarian,
  @JsonValue('vegan')
  vegan,
  @JsonValue('paleo')
  paleo,
  @JsonValue('low_carb')
  lowCarb,
  @JsonValue('high_protein')
  highProtein,
}

extension DietTypeExtension on DietType {
  String get displayName {
    switch (this) {
      case DietType.normal:
        return 'عادي';
      case DietType.keto:
        return 'كيتو';
      case DietType.mediterranean:
        return 'البحر الأبيض المتوسط';
      case DietType.vegetarian:
        return 'نباتي';
      case DietType.vegan:
        return 'نباتي صرف';
      case DietType.paleo:
        return 'باليو';
      case DietType.lowCarb:
        return 'قليل الكربوهيدرات';
      case DietType.highProtein:
        return 'عالي البروتين';
    }
  }

  String get description {
    switch (this) {
      case DietType.normal:
        return 'نظام غذائي متوازن ومتنوع';
      case DietType.keto:
        return 'عالي الدهون، قليل الكربوهيدرات';
      case DietType.mediterranean:
        return 'غني بالخضار والأسماك وزيت الزيتون';
      case DietType.vegetarian:
        return 'بدون لحوم، يشمل منتجات الألبان';
      case DietType.vegan:
        return 'نباتي بالكامل، بدون منتجات حيوانية';
      case DietType.paleo:
        return 'أطعمة طبيعية غير معالجة';
      case DietType.lowCarb:
        return 'تقليل الكربوهيدرات لفقدان الوزن';
      case DietType.highProtein:
        return 'عالي البروتين لبناء العضلات';
    }
  }
}
